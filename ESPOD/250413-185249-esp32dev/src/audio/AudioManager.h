#pragma once

#include <Arduino.h>
#include "AudioTools.h"
#include "AudioTools/AudioLibs/A2DPStream.h"
#include "AudioTools/Disk/AudioSourceSDFAT.h"
#include "AudioTools/AudioCodecs/CodecMP3Helix.h"

// Audio playback states
enum AudioState {
    AUDIO_STOPPED,
    AUDIO_PLAYING,
    AUDIO_PAUSED,
    AUDIO_CONNECTING,
    AUDIO_DISCONNECTED
};

// Audio manager class for handling SD card MP3 playback and Bluetooth A2DP streaming
class AudioManager {
public:
    AudioManager();
    ~AudioManager();

    // Initialize audio system
    bool init(int sd_cs_pin = 5, const char* device_name = "ESPOD");
    
    // Bluetooth device discovery and connection
    bool startDiscovery();
    bool connectToDevice(const char* device_name);
    bool isConnected();
    String getConnectedDeviceName();
    bool disconnect();
    
    // Audio playback controls
    bool play();
    bool pause();
    bool stop();
    bool nextTrack();
    bool previousTrack();
    bool setVolume(int volume); // 0-100
    int getVolume();
    
    // File management
    bool setCurrentFile(const char* filename);
    String getCurrentFile();
    bool hasNextFile();
    bool hasPreviousFile();
    int getFileCount();
    int getCurrentFileIndex();
    
    // State management
    AudioState getState();
    bool isPlaying();
    bool isPaused();
    
    // Main loop function - must be called regularly
    void loop();
    
    // Callbacks for state changes
    void setConnectionCallback(void (*callback)(bool connected, const char* device_name));
    void setPlaybackCallback(void (*callback)(AudioState state, const char* filename));

private:
    // Audio components
    AudioSourceSDFAT* source;
    MP3DecoderHelix* decoder;
    BufferRTOS<uint8_t>* buffer;
    QueueStream<uint8_t>* out;
    AudioPlayer* player;
    BluetoothA2DPSource* a2dp_source;
    
    // State variables
    AudioState current_state;
    String connected_device_name;
    String current_file;
    int current_volume;
    int sd_cs_pin;
    
    // File management
    int file_count;
    int current_file_index;
    
    // Callbacks
    void (*connection_callback)(bool connected, const char* device_name);
    void (*playback_callback)(AudioState state, const char* filename);
    
    // Internal methods
    void updateFileList();
    bool initializeAudioComponents();
    void cleanupAudioComponents();
    static int32_t getData(uint8_t *data, int32_t bytes);
    static bool deviceScanCallback(const char *ssid, esp_bd_addr_t address, int rssi);
    static void connectionStateChanged(esp_a2d_connection_state_t state, void *ptr);
    
    // Static instance for callbacks
    static AudioManager* instance;
};

// Global audio manager instance
extern AudioManager audioManager;
